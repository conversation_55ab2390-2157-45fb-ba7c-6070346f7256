# 🔐 Default Login Credentials

## Test Accounts for Development

The Community Services Administrative System includes several test accounts for development and testing purposes.

### Available Test Accounts

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **Admin** | `<EMAIL>` | `admin123` | Full system access |
| **Manager** | `<EMAIL>` | `manager123` | Competition management |
| **Operator** | `<EMAIL>` | `operator123` | System operations |
| **Demo** | `<EMAIL>` | `demo123` | Read-only access |

### Quick Login

1. **Open the application**: Navigate to `http://localhost:3000`
2. **Auto-redirect**: You'll be redirected to `/login` due to authentication guards
3. **Use test credentials**: Click any credential card on the login page to auto-fill
4. **Or manually enter**: Copy any email/password combination from the table above

### Recommended Test Flow

1. **Start with Admin**: Use `<EMAIL>` / `admin123` for full access
2. **Test different roles**: Try other accounts to see role-based permissions
3. **Explore features**: Navigate through competitions, credits, schools, and admin sections

### Features Available After Login

- **Dashboard**: Overview with statistics and quick actions
- **Competition Management**: Create, edit, and manage competitions
- **Credit Distribution**: Manage credit allocation and distribution
- **School Statistics**: View and analyze school performance data
- **Admin Tools**: User management and system configuration (admin only)

### Development Notes

- **Mock API**: All authentication is handled by mock services
- **Persistent Login**: Sessions persist across browser refreshes
- **Role-based Access**: Different roles have different permissions
- **Real-time Updates**: Mock data updates in real-time during development

### Troubleshooting

If login fails:
1. Check that the development server is running on `localhost:3000`
2. Verify mock API is enabled (should show test credentials on login page)
3. Clear browser cookies and localStorage if needed
4. Check browser console for any JavaScript errors

### Security Note

⚠️ **These are development-only credentials**. In production, these test accounts should be removed and replaced with proper user management and authentication systems.
