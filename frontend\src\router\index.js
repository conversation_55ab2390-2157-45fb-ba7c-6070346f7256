import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Import views
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const HomePage = () => import('@/views/home/<USER>')
const Dashboard = () => import('@/views/dashboard/Dashboard.vue')
const Layout = () => import('@/components/layout/Layout.vue')

const routes = [
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: {
      requiresGuest: true,
      title: 'Login'
    }
  },
  {
    path: '/register',
    name: 'register',
    component: Register,
    meta: {
      requiresGuest: true,
      title: 'Register'
    }
  },
  {
    path: '/',
    component: Layout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'home',
        component: HomePage,
        meta: {
          title: 'Home'
        }
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard,
        meta: {
          title: 'Dashboard'
        }
      },
      {
        path: 'competitions',
        name: 'competitions',
        component: () => import('@/views/competitions/CompetitionList.vue'),
        meta: {
          title: 'Competitions'
        }
      },
      {
        path: 'competitions/create',
        name: 'competition-create',
        component: () => import('@/views/competitions/CompetitionCreate.vue'),
        meta: {
          title: 'Create Competition',
          requiresPermission: 'camp_admin',
          requiresRole: 'admin'
        }
      },
      {
        path: 'credits',
        name: 'credits',
        component: () => import('@/views/credits/CreditManagement.vue'),
        meta: {
          title: 'Credit Management',
          requiresPermission: 'camp_admin',
          requiresRole: 'admin'
        }
      },
      {
        path: 'schools',
        name: 'schools',
        component: () => import('@/views/schools/SchoolStatistics.vue'),
        meta: {
          title: 'School Statistics'
        }
      },
      {
        path: 'admin',
        name: 'admin',
        component: () => import('@/views/admin/AdminTools.vue'),
        meta: {
          title: 'Admin Tools',
          requiresAdmin: true,
          requiresPermission: 'camp_admin'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/error/NotFound.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Community Services Admin` : 'Community Services Admin'

  // Initialize auth state from stored tokens if needed
  if (!authStore.isLoggedIn && !authStore.loading) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.error('Auth initialization failed:', error)
    }
  }

  // Check authentication requirements
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({ name: 'login', query: { redirect: to.fullPath } })
      return
    }

    // Check permissions if specified
    if (to.meta.requiresPermission) {
      const hasPermission = authStore.hasPermission(to.meta.requiresPermission)
      if (!hasPermission) {
        console.warn(`Access denied: Missing permission ${to.meta.requiresPermission}`)
        next({ name: 'dashboard' }) // Redirect to dashboard instead of showing error
        return
      }
    }

    // Check roles if specified
    if (to.meta.requiresRole) {
      const hasRole = authStore.roles.includes(to.meta.requiresRole)
      if (!hasRole) {
        console.warn(`Access denied: Missing role ${to.meta.requiresRole}`)
        next({ name: 'dashboard' })
        return
      }
    }

    // Check admin access
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      console.warn('Access denied: Admin access required')
      next({ name: 'dashboard' })
      return
    }
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect authenticated users away from guest-only pages
    const redirectPath = to.query.redirect || '/dashboard'
    next(redirectPath)
    return
  }

  next()
})

// Global error handler for navigation
router.onError((error) => {
  console.error('Router navigation error:', error)

  // If it's an authentication error, redirect to login
  if (error.message?.includes('auth') || error.message?.includes('token')) {
    const authStore = useAuthStore()
    authStore.logout()
    router.push({ name: 'login' })
  }
})

export default router
