"""
Pydantic schemas for camp admin authentication endpoints.
"""

from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, List
from datetime import datetime


# Request Schemas
class AdminLoginRequest(BaseModel):
    """Request schema for admin login."""
    
    email: EmailStr = Field(..., description="管理员邮箱地址")
    password: str = Field(..., min_length=6, description="密码，最少6位")
    remember_me: Optional[bool] = Field(False, description="记住登录状态")


class AdminRegisterRequest(BaseModel):
    """Request schema for admin registration."""
    
    email: EmailStr = Field(..., description="管理员邮箱地址")
    password: str = Field(..., min_length=8, description="密码，最少8位")
    confirm_password: str = Field(..., description="确认密码")
    username: str = Field(..., min_length=2, max_length=50, description="用户名")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('Passwords do not match')
        return v


class AdminTokenRefreshRequest(BaseModel):
    """Request schema for token refresh."""
    
    refresh_token: str = Field(..., description="刷新令牌")


class AdminChangePasswordRequest(BaseModel):
    """Request schema for password change."""
    
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, description="新密码，最少8位")
    confirm_new_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_new_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('New passwords do not match')
        return v


class AdminLogoutRequest(BaseModel):
    """Request schema for admin logout."""
    
    all_devices: Optional[bool] = Field(False, description="是否登出所有设备")


class AdminForgotPasswordRequest(BaseModel):
    """Request schema for forgot password."""
    
    email: EmailStr = Field(..., description="管理员邮箱地址")


class AdminResetPasswordRequest(BaseModel):
    """Request schema for password reset."""
    
    reset_token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=8, description="新密码，最少8位")
    confirm_new_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_new_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('New passwords do not match')
        return v


# Response Schemas
class AdminTokenResponse(BaseModel):
    """Response schema for authentication tokens."""
    
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="Bearer", description="令牌类型")
    expires_in: int = Field(..., description="令牌过期时间（秒）")


class AdminProfileResponse(BaseModel):
    """Response schema for admin profile."""
    
    email: str = Field(..., description="管理员邮箱")
    username: str = Field(..., description="用户名")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    is_active: bool = Field(True, description="是否激活")
    permissions: List[str] = Field(default_factory=list, description="权限列表")
    roles: List[str] = Field(default_factory=list, description="角色列表")


class AdminLoginResponse(BaseModel):
    """Response schema for admin login."""
    
    token: str = Field(..., description="访问令牌", alias="access_token")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="Bearer", description="令牌类型")
    expires_in: int = Field(..., description="令牌过期时间（秒）")
    admin: AdminProfileResponse = Field(..., description="管理员信息")


class AdminRegisterResponse(BaseModel):
    """Response schema for admin registration."""
    
    email: str = Field(..., description="管理员邮箱")
    username: str = Field(..., description="用户名")
    created_at: datetime = Field(..., description="创建时间")
    message: str = Field(default="Admin registered successfully", description="注册成功消息")


class AdminTokenVerifyResponse(BaseModel):
    """Response schema for token verification."""
    
    valid: bool = Field(..., description="令牌是否有效")
    admin_id: Optional[str] = Field(None, description="管理员ID")
    email: Optional[str] = Field(None, description="管理员邮箱")
    expires_at: Optional[datetime] = Field(None, description="令牌过期时间")
    permissions: List[str] = Field(default_factory=list, description="权限列表")


class AdminSessionResponse(BaseModel):
    """Response schema for admin session info."""
    
    session_id: str = Field(..., description="会话ID")
    admin_id: str = Field(..., description="管理员ID")
    created_at: datetime = Field(..., description="会话创建时间")
    last_activity: datetime = Field(..., description="最后活动时间")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    is_active: bool = Field(True, description="会话是否活跃")


# Error Response Schemas
class AdminAuthErrorResponse(BaseModel):
    """Response schema for authentication errors."""
    
    success: bool = Field(False, description="请求是否成功")
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    details: Optional[dict] = Field(None, description="错误详情")


# Internal Service Response Schemas (for service layer)
class AdminAuthServiceResponse(BaseModel):
    """Internal service response schema."""
    
    success: bool = Field(..., description="操作是否成功")
    data: Optional[dict] = Field(None, description="返回数据")
    error_message: Optional[str] = Field(None, description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")


class AdminTokenData(BaseModel):
    """Token payload data."""
    
    admin_id: str = Field(..., description="管理员ID")
    email: str = Field(..., description="管理员邮箱")
    username: str = Field(..., description="用户名")
    permissions: List[str] = Field(default_factory=list, description="权限列表")
    issued_at: datetime = Field(..., description="令牌签发时间")
    expires_at: datetime = Field(..., description="令牌过期时间")


# Configuration Schemas
class AdminAuthConfig(BaseModel):
    """Authentication configuration."""
    
    jwt_secret_key: str = Field(..., description="JWT密钥")
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间（分钟）")
    refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间（天）")
    password_min_length: int = Field(default=8, description="密码最小长度")
    max_login_attempts: int = Field(default=5, description="最大登录尝试次数")
    lockout_duration_minutes: int = Field(default=15, description="锁定时长（分钟）")


# Validation Schemas
class AdminPasswordValidation(BaseModel):
    """Password validation rules."""
    
    min_length: int = Field(default=8, description="最小长度")
    require_uppercase: bool = Field(default=True, description="需要大写字母")
    require_lowercase: bool = Field(default=True, description="需要小写字母")
    require_numbers: bool = Field(default=True, description="需要数字")
    require_special_chars: bool = Field(default=True, description="需要特殊字符")
    
    @classmethod
    def validate_password(cls, password: str) -> tuple[bool, List[str]]:
        """Validate password against rules."""
        errors = []
        inst = cls()
        
        if len(password) < inst.min_length:
            errors.append(f"Password must be at least {cls.min_length} characters long")
        
        if inst.require_uppercase and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if inst.require_lowercase and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if inst.require_numbers and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if inst.require_special_chars and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("Password must contain at least one special character")
        
        return len(errors) == 0, errors
