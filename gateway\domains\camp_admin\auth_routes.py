"""
Authentication routes for Camp Admin domain.

Handles authentication specific to camp administration including:
- Admin login/logout
- Token management
- Admin profile management
- Session handling
"""

from fastapi import APIRouter, Body, Depends, HTTPException, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import logging
import hashlib
import jwt
from datetime import datetime, timedelta

from .auth_schemas import (
    AdminLoginRequest,
    AdminLoginResponse,
    AdminRegisterRequest,
    AdminProfileResponse,
    AdminTokenRefreshRequest,
    AdminChangePasswordRequest,
    AdminLogoutRequest
)
from .auth_services import camp_admin_auth_services
from core.database.dependencies import (
    get_read_only_session,
    get_transaction_session,
)
from libs.schemas.api.responses import (
    success,
    SuccessResponse,
    ErrorResponse,
)
from libs.auth.authentication import get_current_admin_id

logger = logging.getLogger(__name__)

# Create router with prefix for auth endpoints
router = APIRouter(
    prefix="/auth",
    tags=["camp-admin-auth"],
    responses={404: {"description": "Not found"}},
)

# Security scheme for JWT tokens
security = HTTPBearer()


@router.post(
    "/login",
    response_model=SuccessResponse | ErrorResponse,
    summary="管理员登录",
    description="管理员使用邮箱和密码登录系统",
)
async def admin_login(
    login_data: AdminLoginRequest = Body(...),
    db_session = Depends(get_read_only_session),
    response: Response = None,
):
    """Admin login with email and password."""
    try:
        result = await camp_admin_auth_services.authenticate_admin(
            email=login_data.email,
            password=login_data.password,
            db_session=db_session
        )
        
        if not result.success:
            raise HTTPException(
                status_code=401,
                detail=result.error_message or "Invalid credentials"
            )
        
        # Set HTTP-only cookie for token (optional, for web clients)
        if response and result.data.get('access_token') is not None:
            response.set_cookie(
                key="admin_token",
                value=result.data.get('access_token'),
                httponly=True,
                secure=True,  # Set to True in production with HTTPS
                samesite="lax",
                max_age=result.data.get('expires_in')
            )
        print(result.data,flush=True)
        return success(data=result.data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Admin login error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/register",
    response_model=SuccessResponse | ErrorResponse,
    summary="管理员注册",
    description="注册新的管理员账户（需要超级管理员权限）",
)
async def admin_register(
    register_data: AdminRegisterRequest = Body(...),
    current_admin_id: str = Depends(get_current_admin_id),
    tx_session = Depends(get_transaction_session),
):
    """Register a new admin account (requires super admin privileges)."""
    try:
        # TODO: Check if current admin has super admin privileges
        
        result = await camp_admin_auth_services.register_admin(
            email=register_data.email,
            password=register_data.password,
            username=register_data.username,
            created_by=current_admin_id,
            db_session=tx_session
        )
        
        if not result.success:
            raise HTTPException(
                status_code=400,
                detail=result.error_message or "Registration failed"
            )
        
        return success(data=result.data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Admin registration error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/logout",
    response_model=SuccessResponse | ErrorResponse,
    summary="管理员登出",
    description="管理员登出系统，使令牌失效",
)
async def admin_logout(
    logout_data: AdminLogoutRequest = Body(None),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    response: Response = None,
):
    """Admin logout and invalidate token."""
    try:
        token = credentials.credentials if credentials else None
        
        result = await camp_admin_auth_services.logout_admin(token=token)
        
        # Clear HTTP-only cookie
        if response:
            response.delete_cookie(key="admin_token")
        
        return success(data={"message": "Logged out successfully"})
        
    except Exception as e:
        logger.error(f"Admin logout error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/me",
    response_model=SuccessResponse | ErrorResponse,
    summary="获取当前管理员信息",
    description="获取当前登录管理员的个人信息",
)
async def get_current_admin_profile(
    current_admin_id: str = Depends(get_current_admin_id),
    db_session = Depends(get_read_only_session),
):
    """Get current admin profile information."""
    try:
        result = await camp_admin_auth_services.get_admin_profile(
            admin_id=current_admin_id,
            db_session=db_session
        )
        
        if not result.success:
            raise HTTPException(
                status_code=404,
                detail=result.error_message or "Admin not found"
            )
        
        return success(data=result.data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get admin profile error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/refresh",
    response_model=SuccessResponse | ErrorResponse,
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌",
)
async def refresh_admin_token(
    refresh_data: AdminTokenRefreshRequest = Body(...),
):
    """Refresh admin access token."""
    try:
        result = await camp_admin_auth_services.refresh_token(
            refresh_token=refresh_data.refresh_token
        )
        
        if not result.success:
            raise HTTPException(
                status_code=401,
                detail=result.error_message or "Invalid refresh token"
            )
        
        return success(data=result.data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/change-password",
    response_model=SuccessResponse | ErrorResponse,
    summary="修改密码",
    description="管理员修改自己的密码",
)
async def change_admin_password(
    password_data: AdminChangePasswordRequest = Body(...),
    current_admin_id: str = Depends(get_current_admin_id),
    tx_session = Depends(get_transaction_session),
):
    """Change admin password."""
    try:
        result = await camp_admin_auth_services.change_password(
            admin_id=current_admin_id,
            current_password=password_data.current_password,
            new_password=password_data.new_password,
            db_session=tx_session
        )
        
        if not result.success:
            raise HTTPException(
                status_code=400,
                detail=result.error_message or "Password change failed"
            )
        
        return success(data={"message": "Password changed successfully"})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/verify",
    response_model=SuccessResponse | ErrorResponse,
    summary="验证访问令牌",
    description="验证访问令牌的有效性",
)
async def verify_admin_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
):
    """Verify admin access token."""
    try:
        token = credentials.credentials if credentials else None
        
        if not token:
            raise HTTPException(status_code=401, detail="Token required")
        
        result = await camp_admin_auth_services.verify_token(token=token)
        
        if not result.success:
            raise HTTPException(
                status_code=401,
                detail=result.error_message or "Invalid token"
            )
        
        return success(data=result.data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/forgot-password",
    response_model=SuccessResponse | ErrorResponse,
    summary="忘记密码",
    description="发送密码重置邮件",
)
async def forgot_admin_password(
    email: str = Body(..., embed=True),
    db_session = Depends(get_read_only_session),
):
    """Send password reset email."""
    try:
        result = await camp_admin_auth_services.forgot_password(
            email=email,
            db_session=db_session
        )
        
        # Always return success for security reasons (don't reveal if email exists)
        return success(data={"message": "If the email exists, a reset link has been sent"})
        
    except Exception as e:
        logger.error(f"Forgot password error: {e}")
        # Still return success for security
        return success(data={"message": "If the email exists, a reset link has been sent"})


@router.post(
    "/reset-password",
    response_model=SuccessResponse | ErrorResponse,
    summary="重置密码",
    description="使用重置令牌重置密码",
)
async def reset_admin_password(
    reset_token: str = Body(...),
    new_password: str = Body(...),
    tx_session = Depends(get_transaction_session),
):
    """Reset admin password with reset token."""
    try:
        result = await camp_admin_auth_services.reset_password(
            reset_token=reset_token,
            new_password=new_password,
            db_session=tx_session
        )
        
        if not result.success:
            raise HTTPException(
                status_code=400,
                detail=result.error_message or "Password reset failed"
            )
        
        return success(data={"message": "Password reset successfully"})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
