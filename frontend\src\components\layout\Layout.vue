<template>
  <el-container class="layout-container">
    <!-- Sidebar -->
    <el-aside :width="sidebarCollapsed ? '64px' : '250px'" class="sidebar">
      <div class="logo" @click="$router.push('/')" title="Go to Dashboard">
        <span v-if="!sidebarCollapsed" class="logo-text">Admin Panel</span>
        <el-icon v-else class="logo-icon"><Setting /></el-icon>
      </div>
      
      <el-menu
        :default-active="$route.path"
        class="sidebar-menu"
        :collapse="sidebarCollapsed"
        router
        background-color="var(--sidebar-bg)"
        text-color="var(--sidebar-text)"
        active-text-color="var(--sidebar-active)"
      >
        <el-menu-item index="/" class="home-menu-item">
          <el-icon><House /></el-icon>
          <span>Home</span>
        </el-menu-item>

        <el-menu-item index="/dashboard">
          <el-icon><DataAnalysis /></el-icon>
          <span>Dashboard</span>
        </el-menu-item>
        
        <el-menu-item index="/competitions">
          <el-icon><Trophy /></el-icon>
          <span>Competitions</span>
        </el-menu-item>

        <el-menu-item index="/credits">
          <el-icon><Coin /></el-icon>
          <span>Credits</span>
        </el-menu-item>

        <el-menu-item index="/schools">
          <el-icon><School /></el-icon>
          <span>Schools</span>
        </el-menu-item>

        <el-menu-item index="/admin">
          <el-icon><Tools /></el-icon>
          <span>Admin Tools</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- Main content area -->
    <el-container>
      <!-- Usage Guidelines Banner -->
      <div class="usage-banner">
        <el-alert
          title="Platform Notice"
          type="warning"
          :closable="false"
          show-icon
          class="usage-alert"
        >
          <template #default>
            <span class="banner-text">
              🔒 Internal use only - Do not share access links |
              📊 Large queries (>10K records): Contact data department |
              💻 Keep browser open during queries
            </span>
          </template>
        </el-alert>
      </div>

      <!-- Header -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>

          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.name"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- Theme toggle -->
          <el-button
            type="text"
            @click="themeStore.toggleTheme()"
            class="theme-toggle"
            :title="themeStore.themeLabel"
          >
            <el-icon><component :is="themeStore.themeIcon" /></el-icon>
          </el-button>

          <!-- User dropdown -->
          <el-dropdown @command="handleUserCommand">
            <span class="user-dropdown">
              <el-avatar :size="32" class="user-avatar">
                {{ userInitials }}
              </el-avatar>
              <span class="username">{{ authStore.user?.name || authStore.user?.email }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  Profile
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  Settings
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  Logout
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- Main content -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import {
  House,
  Trophy,
  Coin,
  School,
  Tools,
  Setting,
  Expand,
  Fold,
  User,
  SwitchButton,
  ArrowDown,
  Sunny,
  Moon,
  DataAnalysis
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const themeStore = useThemeStore()

// Sidebar state
const sidebarCollapsed = ref(false)

// Computed properties
const userInitials = computed(() => {
  const user = authStore.user
  if (user?.name) {
    return user.name.split(' ').map(n => n[0]).join('').toUpperCase()
  }
  if (user?.email) {
    return user.email[0].toUpperCase()
  }
  return 'U'
})

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    name: item.name,
    title: item.meta.title,
    path: item.path
  }))
})

// Methods
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      // Navigate to profile page
      break
    case 'settings':
      // Navigate to settings page
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      break
  }
}

// Watch for route changes to update active menu item
watch(
  () => route.name,
  () => {
    // Update active menu item if needed
  }
)
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
}

.usage-banner {
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 0;

  .usage-alert {
    margin: 0;
    border: none;
    border-radius: 0;
    background-color: transparent;

    .banner-text {
      font-size: 13px;
      color: var(--text-color-secondary);
      font-weight: 500;
    }
  }
}

.sidebar {
  background-color: var(--sidebar-bg);
  transition: width 0.3s, background-color 0.3s;

  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .logo-img {
      height: 32px;
      margin-right: 10px;
    }

    .logo-text {
      color: #fff;
      font-size: 18px;
      font-weight: 600;
    }

    .logo-icon {
      color: #fff;
      font-size: 24px;
    }
  }
  
  .sidebar-menu {
    border: none;

    .el-menu-item {
      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }

      &.home-menu-item {
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 8px;
        font-weight: 600;
      }
    }
  }
}

.header {
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  transition: background-color 0.3s, border-color 0.3s;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .sidebar-toggle {
      margin-right: 20px;
      font-size: 18px;
    }
    
    .breadcrumb {
      font-size: 14px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .theme-toggle {
      font-size: 18px;
      color: var(--text-color);
      transition: color 0.3s;

      &:hover {
        color: var(--sidebar-active);
      }
    }

    .user-dropdown {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 5px 10px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: var(--bg-color);
      }

      .user-avatar {
        margin-right: 8px;
      }

      .username {
        margin-right: 8px;
        font-size: 14px;
        color: var(--text-color-secondary);
      }
    }
  }
}

.main-content {
  background-color: var(--bg-color);
  padding: 20px;
  overflow-y: auto;
  transition: background-color 0.3s;
}
</style>
