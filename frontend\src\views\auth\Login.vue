<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1 class="login-title">Community Services Admin</h1>
        <p class="login-subtitle">Sign in to your account</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="email">
          <el-input
            v-model="loginForm.email"
            type="email"
            placeholder="Email address"
            size="large"
            :prefix-icon="User"
            :disabled="authStore.loading"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="Password"
            size="large"
            :prefix-icon="Lock"
            :disabled="authStore.loading"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="loginForm.remember">
            Remember me
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.loading"
            @click="handleLogin"
          >
            Sign In
          </el-button>
        </el-form-item>

        <div v-if="authStore.error" class="error-message">
          <el-alert
            :title="authStore.error"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </el-form>

      <div class="login-footer">
        <p>
          Don't have an account?
          <router-link to="/register" class="register-link">
            Sign up here
          </router-link>
        </p>
      </div>
    </div>

    <!-- Test Credentials (Development Mode) -->
    <div class="test-credentials-container" v-if="isDevelopment">
      <TestCredentials @credential-select="handleCredentialSelect" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import TestCredentials from '@/components/common/TestCredentials.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Form ref
const loginFormRef = ref()

// Form data
const loginForm = reactive({
  email: '',
  password: '',
  remember: false
})

// Check if in development mode
const isDevelopment = computed(() => {
  return import.meta.env.DEV || import.meta.env.VITE_MOCK_API !== 'false'
})

// Form validation rules
const loginRules = {
  email: [
    { required: true, message: 'Please enter your email address', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter your password', trigger: 'blur' },
    { min: 8, message: 'Password must be at least 8 characters', trigger: 'blur' }
  ]
}

// Methods
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const result = await authStore.login({
      email: loginForm.email,
      password: loginForm.password,
      remember_me: loginForm.remember
    })

    if (result.success) {
      ElMessage.success('Login successful!')

      // Redirect to intended page or dashboard - use window.location to bypass router guards
      const redirectPath = route.query.redirect || '/dashboard'
      window.location.href = redirectPath
    } else {
      // Error is already set in the store and displayed in the template
      ElMessage.error(result.error || 'Login failed')
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('An unexpected error occurred during login')
  }
}

// Handle credential selection from test credentials
const handleCredentialSelect = (credentials) => {
  loginForm.email = credentials.email
  loginForm.password = credentials.password
  ElMessage.success(`Filled with ${credentials.role} credentials`)
}

// Clear error when form changes
const clearError = () => {
  if (authStore.error) {
    authStore.error = null
  }
}

onMounted(() => {
  // Clear any existing errors
  authStore.error = null
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
  
  .login-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
  }
  
  .login-subtitle {
    color: #7f8c8d;
    font-size: 16px;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
  }
}

.error-message {
  margin-top: 16px;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  
  p {
    color: #7f8c8d;
    font-size: 14px;
  }
  
  .register-link {
    color: #409eff;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.test-credentials-container {
  margin-top: 20px;
  max-width: 600px;
  width: 100%;
}

// Responsive design
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .login-header .login-title {
    font-size: 24px;
  }

  .test-credentials-container {
    margin-top: 16px;
  }
}
</style>
