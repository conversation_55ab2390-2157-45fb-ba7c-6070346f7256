<template>
  <el-card class="test-credentials" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Key /></el-icon>
        <span>Test Login Credentials</span>
      </div>
    </template>
    
    <div class="credentials-list">
      <div 
        v-for="(cred, role) in credentials" 
        :key="role"
        class="credential-item"
        @click="fillCredentials(cred)"
      >
        <div class="role-info">
          <el-tag :type="getRoleType(role)" size="small">{{ role.toUpperCase() }}</el-tag>
          <span class="role-description">{{ getRoleDescription(role) }}</span>
        </div>
        <div class="credential-details">
          <div class="credential-field">
            <span class="label">Email:</span>
            <code>{{ cred.email }}</code>
          </div>
          <div class="credential-field">
            <span class="label">Password:</span>
            <code>{{ cred.password }}</code>
          </div>
        </div>
        <el-button 
          type="primary" 
          size="small" 
          text
          @click.stop="copyCredentials(cred)"
        >
          Copy
        </el-button>
      </div>
    </div>
    
    <div class="credentials-note">
      <el-alert
        title="Development Mode"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          These are test credentials for development. Click any credential to auto-fill the login form.
        </template>
      </el-alert>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Key } from '@element-plus/icons-vue'
import { getTestCredentials } from '@/services/mockApiHandler.js'

// Props
const props = defineProps({
  onCredentialSelect: {
    type: Function,
    default: null
  }
})

// Get test credentials
const credentials = computed(() => getTestCredentials())

// Methods
const getRoleType = (role) => {
  const types = {
    admin: 'danger',
    manager: 'warning', 
    operator: 'primary',
    demo: 'success'
  }
  return types[role] || 'info'
}

const getRoleDescription = (role) => {
  const descriptions = {
    admin: 'Full system access',
    manager: 'Competition management',
    operator: 'System operations',
    demo: 'Read-only access'
  }
  return descriptions[role] || 'Standard user'
}

const fillCredentials = (cred) => {
  if (props.onCredentialSelect) {
    props.onCredentialSelect(cred)
  }
}

const copyCredentials = async (cred) => {
  try {
    const text = `Email: ${cred.email}\nPassword: ${cred.password}`
    await navigator.clipboard.writeText(text)
    ElMessage.success('Credentials copied to clipboard!')
  } catch (error) {
    ElMessage.error('Failed to copy credentials')
  }
}
</script>

<style lang="scss" scoped>
.test-credentials {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .credentials-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .credential-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  .role-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
    
    .role-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
  
  .credential-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 0 16px;
  }
  
  .credential-field {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    
    .label {
      color: var(--el-text-color-regular);
      min-width: 60px;
    }
    
    code {
      background-color: var(--el-fill-color-light);
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
  }
  
  .credentials-note {
    margin-top: 16px;
  }
}

@media (max-width: 768px) {
  .credential-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .role-info,
    .credential-details {
      margin: 0;
    }
  }
}
</style>
